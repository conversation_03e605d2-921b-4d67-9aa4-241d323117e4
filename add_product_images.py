#!/usr/bin/env python3
"""
<PERSON>ript to add sample product images to CraftConnect
"""

import os
import requests
from PIL import Image, ImageDraw, ImageFont
from database import get_db
import io

def create_sample_image(product_name, category, size=(400, 400)):
    """Create a sample product image with text overlay"""
    
    # Color schemes for different categories
    color_schemes = {
        'pottery': ('#8B4513', '#DEB887', '#F4A460'),  # Browns and sandy colors
        'woodwork': ('#654321', '#D2691E', '#DEB887'),  # Wood tones
        'textiles': ('#4B0082', '#9370DB', '#E6E6FA'),  # Purple/lavender
        'jewelry': ('#FFD700', '#FFA500', '#FFFACD'),  # Gold tones
        'paintings': ('#FF6347', '#FFB6C1', '#FFF8DC'),  # Artistic colors
        'metalwork': ('#708090', '#C0C0C0', '#F5F5F5'),  # Metal tones
        'other': ('#696969', '#A9A9A9', '#D3D3D3')  # Gray tones
    }
    
    colors = color_schemes.get(category, color_schemes['other'])
    
    # Create image
    img = Image.new('RGB', size, colors[2])
    draw = ImageDraw.Draw(img)
    
    # Draw background pattern
    for i in range(0, size[0], 50):
        for j in range(0, size[1], 50):
            if (i + j) % 100 == 0:
                draw.rectangle([i, j, i+25, j+25], fill=colors[1])
    
    # Add border
    border_width = 10
    draw.rectangle([0, 0, size[0], border_width], fill=colors[0])
    draw.rectangle([0, 0, border_width, size[1]], fill=colors[0])
    draw.rectangle([size[0]-border_width, 0, size[0], size[1]], fill=colors[0])
    draw.rectangle([0, size[1]-border_width, size[0], size[1]], fill=colors[0])
    
    # Add text
    try:
        # Try to use a nice font
        font_large = ImageFont.truetype("arial.ttf", 24)
        font_small = ImageFont.truetype("arial.ttf", 16)
    except:
        # Fallback to default font
        font_large = ImageFont.load_default()
        font_small = ImageFont.load_default()
    
    # Product name
    text_bbox = draw.textbbox((0, 0), product_name, font=font_large)
    text_width = text_bbox[2] - text_bbox[0]
    text_height = text_bbox[3] - text_bbox[1]
    
    x = (size[0] - text_width) // 2
    y = size[1] // 2 - 40
    
    # Add text shadow
    draw.text((x+2, y+2), product_name, fill='black', font=font_large)
    draw.text((x, y), product_name, fill='white', font=font_large)
    
    # Category label
    category_text = f"Category: {category.title()}"
    cat_bbox = draw.textbbox((0, 0), category_text, font=font_small)
    cat_width = cat_bbox[2] - cat_bbox[0]
    
    x_cat = (size[0] - cat_width) // 2
    y_cat = y + text_height + 20
    
    draw.text((x_cat+1, y_cat+1), category_text, fill='black', font=font_small)
    draw.text((x_cat, y_cat), category_text, fill=colors[0], font=font_small)
    
    return img

def save_product_images():
    """Create and save sample product images"""
    
    # Ensure uploads directory exists
    uploads_dir = os.path.join('static', 'uploads')
    os.makedirs(uploads_dir, exist_ok=True)
    
    # Sample products with their details
    sample_products = [
        {'name': 'Handcrafted Ceramic Vase', 'category': 'pottery', 'filename': 'ceramic_vase.jpg'},
        {'name': 'Walnut Jewelry Box', 'category': 'woodwork', 'filename': 'jewelry_box.jpg'},
        {'name': 'Hand-Woven Silk Scarf', 'category': 'textiles', 'filename': 'silk_scarf.jpg'},
        {'name': 'Sterling Silver Ring Set', 'category': 'jewelry', 'filename': 'silver_rings.jpg'},
        {'name': 'Tuscan Landscape Painting', 'category': 'paintings', 'filename': 'landscape_painting.jpg'},
        {'name': 'Live Edge Cutting Board', 'category': 'woodwork', 'filename': 'cutting_board.jpg'},
        {'name': 'Artisan Coffee Mug', 'category': 'pottery', 'filename': 'coffee_mug.jpg'},
        {'name': 'Copper Wind Chimes', 'category': 'metalwork', 'filename': 'wind_chimes.jpg'},
        {'name': 'Cotton Throw Blanket', 'category': 'textiles', 'filename': 'throw_blanket.jpg'},
        {'name': 'Watercolor Art Series', 'category': 'paintings', 'filename': 'watercolor_art.jpg'},
    ]
    
    created_images = []
    
    for product in sample_products:
        try:
            # Create the image
            img = create_sample_image(product['name'], product['category'])
            
            # Save the image
            image_path = os.path.join(uploads_dir, product['filename'])
            img.save(image_path, 'JPEG', quality=85)
            
            created_images.append({
                'filename': product['filename'],
                'path': f"/static/uploads/{product['filename']}",
                'name': product['name'],
                'category': product['category']
            })
            
            print(f"✓ Created image: {product['filename']}")
            
        except Exception as e:
            print(f"✗ Error creating image for {product['name']}: {str(e)}")
    
    return created_images

def update_products_with_images():
    """Update existing products in database with image paths"""
    
    try:
        db = get_db()
        
        # Create sample images
        print("🎨 Creating sample product images...")
        created_images = save_product_images()
        
        # Update products with image paths
        print("\n📦 Updating products with image paths...")
        
        updated_count = 0
        for img_data in created_images:
            # Find products that match this image (by name similarity)
            query = {"name": {"$regex": img_data['name'].split()[0], "$options": "i"}}
            products = list(db.products.find(query))
            
            for product in products:
                # Update the product with image path
                result = db.products.update_one(
                    {"_id": product["_id"]},
                    {"$set": {"image_path": img_data['path']}}
                )
                
                if result.modified_count > 0:
                    print(f"✓ Updated {product['name']} with image")
                    updated_count += 1
        
        print(f"\n✅ Successfully updated {updated_count} products with images!")
        print(f"📁 Created {len(created_images)} sample images in static/uploads/")
        
        return True
        
    except Exception as e:
        print(f"❌ Error updating products: {str(e)}")
        return False

def main():
    """Main function"""
    print("🖼️ Adding Product Images to CraftConnect")
    print("=" * 45)
    
    if update_products_with_images():
        print("\n🌐 View updated products at: http://127.0.0.1:5000/marketplace/")
        print("📸 All products now have sample images!")
    else:
        print("\n⚠️ Some errors occurred. Check the output above.")

if __name__ == "__main__":
    main()
