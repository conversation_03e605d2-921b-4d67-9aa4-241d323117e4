from flask import Blueprint, render_template, request, jsonify, session, redirect, url_for
from database import get_db
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime, timezone
import re

auth_bp = Blueprint('auth', __name__, url_prefix='/auth')

@auth_bp.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        data = request.get_json()
        email = data.get('email')
        password = data.get('password')
        
        db = get_db()
        user = db.users.find_one({'email': email})
        
        if user and check_password_hash(user['password'], password):
            session['user_id'] = str(user['_id'])
            session['user_email'] = user['email']
            return jsonify({'success': True, 'message': 'Login successful'})
        
        return jsonify({'success': False, 'message': 'Invalid credentials'}), 401
    
    return render_template('auth/login.html')

@auth_bp.route('/register', methods=['GET', 'POST'])
def register():
    if request.method == 'POST':
        data = request.get_json()
        email = data.get('email')
        password = data.get('password')
        name = data.get('name')
        
        # Validation
        if not email or not password or not name:
            return jsonify({'success': False, 'message': 'All fields required'}), 400
        
        if not re.match(r'^[^@]+@[^@]+\.[^@]+$', email):
            return jsonify({'success': False, 'message': 'Invalid email'}), 400
        
        db = get_db()
        if db.users.find_one({'email': email}):
            return jsonify({'success': False, 'message': 'Email already exists'}), 400
        
        user_data = {
            'name': name,
            'email': email,
            'password': generate_password_hash(password),
            'role': 'user',
            'created_at': datetime.now(timezone.utc)
        }
        
        result = db.users.insert_one(user_data)
        session['user_id'] = str(result.inserted_id)
        session['user_email'] = email
        
        return jsonify({'success': True, 'message': 'Registration successful'})
    
    return render_template('auth/register.html')

@auth_bp.route('/logout')
def logout():
    session.clear()
    return redirect(url_for('home'))

