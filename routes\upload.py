from flask import Blueprint, request, jsonify, render_template, session, redirect, url_for
from database import get_db
from datetime import datetime, timezone
import os

upload_bp = Blueprint('upload', __name__, url_prefix='/upload')

@upload_bp.before_request
def require_login():
    if 'user_id' not in session:
        return redirect(url_for('auth.login'))

@upload_bp.route('/product', methods=['GET', 'POST'])
def upload_product():
    if request.method == 'POST':
        try:
            # Get form data
            name = request.form.get('name')
            category = request.form.get('category')
            description = request.form.get('description')
            price = float(request.form.get('price', 0))
            quantity = int(request.form.get('quantity', 1))

            if not name or not category:
                return jsonify({'success': False, 'message': 'Name and category are required'}), 400

            # Handle file upload
            image_path = None
            if 'image' in request.files:
                file = request.files['image']
                if file and file.filename:
                    # Basic file validation
                    allowed_extensions = {'png', 'jpg', 'jpeg', 'gif'}
                    if file.filename and '.' in file.filename and \
                       file.filename.rsplit('.', 1)[1].lower() in allowed_extensions:

                        # Create uploads directory if it doesn't exist
                        upload_dir = os.path.join('static', 'uploads')
                        os.makedirs(upload_dir, exist_ok=True)

                        # Save file with unique name
                        filename = f"{session['user_id']}_{datetime.now(timezone.utc).strftime('%Y%m%d_%H%M%S')}_{file.filename}"
                        file_path = os.path.join(upload_dir, filename)
                        file.save(file_path)
                        image_path = f"/static/uploads/{filename}"

            # Save to database
            db = get_db()
            product_data = {
                'user_id': session['user_id'],
                'name': name,
                'category': category,
                'description': description,
                'price': price,
                'quantity': quantity,
                'image_path': image_path,
                'created_at': datetime.now(timezone.utc)
            }

            result = db.products.insert_one(product_data)

            return jsonify({
                'success': True,
                'message': 'Product uploaded successfully!',
                'product_id': str(result.inserted_id)
            })

        except Exception as e:
            return jsonify({'success': False, 'message': f'Error uploading product: {str(e)}'}), 500

    return render_template('upload/product.html')

@upload_bp.route('/image', methods=['POST'])
def upload_image():
    if 'file' not in request.files:
        return jsonify({'error': 'No file provided'}), 400
    
    file = request.files['file']
    if file.filename == '':
        return jsonify({'error': 'No file selected'}), 400
    
    # Basic file validation
    allowed_extensions = {'png', 'jpg', 'jpeg', 'gif'}
    if not (file.filename and '.' in file.filename and
            file.filename.rsplit('.', 1)[1].lower() in allowed_extensions):
        return jsonify({'error': 'Invalid file type'}), 400
    
    return jsonify({'success': True, 'message': 'File uploaded successfully'})
