from flask import Blueprint, render_template, session, redirect, url_for, jsonify
from database import get_db
from datetime import datetime, timezone

admin_bp = Blueprint('admin', __name__, url_prefix='/admin')

@admin_bp.before_request
def require_admin():
    if 'user_id' not in session:
        return redirect(url_for('auth.login'))
    # In a real app, you'd check if user is admin here

@admin_bp.route('/')
def admin_dashboard():
    return render_template('admin/dashboard.html')

@admin_bp.route('/add-sample-products')
def add_sample_products():
    """Add sample products to the database"""
    try:
        db = get_db()

        # Sample products
        products = [
            {
                'user_id': 'sample_user_1',
                'name': 'Handcrafted Ceramic Vase',
                'description': 'Beautiful ceramic vase with traditional Southwest patterns. Hand-thrown on the wheel and fired in a wood kiln.',
                'category': 'pottery',
                'price': 85.00,
                'quantity': 3,
                'ai_quality_score': 0.92,
                'created_at': datetime.now(timezone.utc)
            },
            {
                'user_id': 'sample_user_2',
                'name': 'Handcrafted Walnut Jewelry Box',
                'description': 'Elegant jewelry box made from sustainably sourced black walnut with traditional dovetail joints.',
                'category': 'woodwork',
                'price': 185.00,
                'quantity': 1,
                'ai_quality_score': 0.95,
                'created_at': datetime.now(timezone.utc)
            },
            {
                'user_id': 'sample_user_3',
                'name': 'Hand-Woven Silk Scarf',
                'description': 'Luxurious silk scarf hand-woven on a traditional loom with intricate geometric patterns.',
                'category': 'textiles',
                'price': 95.00,
                'quantity': 5,
                'ai_quality_score': 0.93,
                'created_at': datetime.now(timezone.utc)
            },
            {
                'user_id': 'sample_user_4',
                'name': 'Sterling Silver Ring Set',
                'description': 'Set of three stackable sterling silver rings with hammered texture. Hand-forged and unique.',
                'category': 'jewelry',
                'price': 125.00,
                'quantity': 4,
                'ai_quality_score': 0.94,
                'created_at': datetime.now(timezone.utc)
            },
            {
                'user_id': 'sample_user_5',
                'name': 'Tuscan Landscape Oil Painting',
                'description': 'Original oil painting of rolling Tuscan hills during golden hour. Painted en plein air.',
                'category': 'paintings',
                'price': 450.00,
                'quantity': 1,
                'ai_quality_score': 0.97,
                'created_at': datetime.now(timezone.utc)
            },
            {
                'user_id': 'sample_user_2',
                'name': 'Live Edge Cutting Board',
                'description': 'Beautiful cutting board made from a single piece of maple with natural live edge.',
                'category': 'woodwork',
                'price': 75.00,
                'quantity': 4,
                'ai_quality_score': 0.91,
                'created_at': datetime.now(timezone.utc)
            },
            {
                'user_id': 'sample_user_1',
                'name': 'Artisan Coffee Mug',
                'description': 'Hand-thrown coffee mug with comfortable handle and beautiful blue-green glaze.',
                'category': 'pottery',
                'price': 28.00,
                'quantity': 8,
                'ai_quality_score': 0.87,
                'created_at': datetime.now(timezone.utc)
            },
            {
                'user_id': 'sample_user_4',
                'name': 'Copper Wind Chimes',
                'description': 'Handcrafted wind chimes made from recycled copper pipes with beautiful, soothing tones.',
                'category': 'metalwork',
                'price': 78.00,
                'quantity': 7,
                'ai_quality_score': 0.86,
                'created_at': datetime.now(timezone.utc)
            },
            {
                'user_id': 'sample_user_3',
                'name': 'Organic Cotton Throw Blanket',
                'description': 'Cozy throw blanket made from organic cotton and hand-dyed with natural indigo.',
                'category': 'textiles',
                'price': 145.00,
                'quantity': 3,
                'ai_quality_score': 0.90,
                'created_at': datetime.now(timezone.utc)
            },
            {
                'user_id': 'sample_user_5',
                'name': 'Abstract Watercolor Series',
                'description': 'Set of three abstract watercolor paintings inspired by natural elements.',
                'category': 'paintings',
                'price': 280.00,
                'quantity': 1,
                'ai_quality_score': 0.91,
                'created_at': datetime.now(timezone.utc)
            }
        ]

        # Check if products already exist
        existing_count = db.products.count_documents({})

        # Insert products
        result = db.products.insert_many(products)

        return jsonify({
            'success': True,
            'message': f'Successfully added {len(result.inserted_ids)} sample products!',
            'products_added': len(result.inserted_ids),
            'total_products': existing_count + len(result.inserted_ids)
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Error adding products: {str(e)}'
        }), 500

@admin_bp.route('/add-product-images')
def add_product_images():
    """Add sample images to existing products"""
    try:
        from PIL import Image, ImageDraw, ImageFont
        import os

        db = get_db()

        # Ensure uploads directory exists
        uploads_dir = os.path.join('static', 'uploads')
        if not os.path.exists(uploads_dir):
            os.makedirs(uploads_dir, exist_ok=True)

        # Sample image data for products
        image_mappings = [
            {'name_pattern': 'Ceramic Vase', 'filename': 'ceramic_vase.jpg', 'category': 'pottery'},
            {'name_pattern': 'Jewelry Box', 'filename': 'jewelry_box.jpg', 'category': 'woodwork'},
            {'name_pattern': 'Silk Scarf', 'filename': 'silk_scarf.jpg', 'category': 'textiles'},
            {'name_pattern': 'Ring Set', 'filename': 'silver_rings.jpg', 'category': 'jewelry'},
            {'name_pattern': 'Painting', 'filename': 'landscape_painting.jpg', 'category': 'paintings'},
            {'name_pattern': 'Cutting Board', 'filename': 'cutting_board.jpg', 'category': 'woodwork'},
            {'name_pattern': 'Coffee Mug', 'filename': 'coffee_mug.jpg', 'category': 'pottery'},
            {'name_pattern': 'Wind Chimes', 'filename': 'wind_chimes.jpg', 'category': 'metalwork'},
            {'name_pattern': 'Blanket', 'filename': 'throw_blanket.jpg', 'category': 'textiles'},
            {'name_pattern': 'Watercolor', 'filename': 'watercolor_art.jpg', 'category': 'paintings'},
        ]

        updated_count = 0
        created_images = 0

        for mapping in image_mappings:
            image_path = os.path.join(uploads_dir, mapping['filename'])

            # Only create image if it doesn't exist
            if not os.path.exists(image_path):
                try:
                    # Create a simple placeholder image
                    img = Image.new('RGB', (400, 300), color='lightgray')
                    draw = ImageDraw.Draw(img)

                    # Add simple text
                    try:
                        font = ImageFont.load_default()
                        text = f"{mapping['name_pattern']}\n({mapping['category']})"
                        draw.text((50, 120), text, fill='black', font=font)
                    except:
                        pass

                    # Save image
                    img.save(image_path, 'JPEG')
                    created_images += 1

                except Exception as img_error:
                    print(f"Error creating image {mapping['filename']}: {img_error}")
                    continue

            # Update products that match this pattern (regardless of image creation)
            query = {"name": {"$regex": mapping['name_pattern'], "$options": "i"}}
            result = db.products.update_many(
                query,
                {"$set": {"image_path": f"/static/uploads/{mapping['filename']}"}}
            )
            updated_count += result.modified_count

        return jsonify({
            'success': True,
            'message': f'Successfully added images to {updated_count} products!',
            'images_created': created_images,
            'products_updated': updated_count
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Error adding images: {str(e)}'
        }), 500

@admin_bp.route('/setup-complete-demo')
def setup_complete_demo():
    """Add sample products AND images in one go"""
    try:
        from PIL import Image, ImageDraw, ImageFont
        import os

        db = get_db()

        # Step 1: Add sample products with detailed info
        sample_products = [
            {
                'user_id': 'demo_artisan_1',
                'name': 'Handcrafted Ceramic Vase',
                'description': 'Beautiful ceramic vase with traditional Southwest patterns. Hand-thrown on the wheel and fired in a wood kiln. Perfect for displaying fresh flowers or as a standalone decorative piece.',
                'category': 'pottery',
                'price': 85.00,
                'quantity': 3,
                'ai_quality_score': 0.92,
                'materials': ['Clay', 'Natural Glazes', 'Wood Ash'],
                'dimensions': '12" H x 6" W',
                'weight': '2.5 lbs',
                'created_at': datetime.now(timezone.utc)
            },
            {
                'user_id': 'demo_artisan_2',
                'name': 'Handcrafted Walnut Jewelry Box',
                'description': 'Elegant jewelry box made from sustainably sourced black walnut. Features multiple compartments, soft felt lining, and traditional dovetail joints. A perfect heirloom piece.',
                'category': 'woodwork',
                'price': 185.00,
                'quantity': 1,
                'ai_quality_score': 0.95,
                'materials': ['Black Walnut', 'Felt Lining', 'Brass Hardware'],
                'dimensions': '10" L x 7" W x 4" H',
                'weight': '2.8 lbs',
                'created_at': datetime.now(timezone.utc)
            },
            {
                'user_id': 'demo_artisan_3',
                'name': 'Hand-Woven Silk Scarf',
                'description': 'Luxurious silk scarf hand-woven on a traditional loom. Features intricate geometric patterns inspired by West African textiles. Naturally dyed with plant-based colors.',
                'category': 'textiles',
                'price': 95.00,
                'quantity': 5,
                'ai_quality_score': 0.93,
                'materials': ['100% Silk', 'Natural Plant Dyes'],
                'dimensions': '60" L x 12" W',
                'weight': '0.3 lbs',
                'created_at': datetime.now(timezone.utc)
            },
            {
                'user_id': 'demo_artisan_4',
                'name': 'Sterling Silver Ring Set',
                'description': 'Set of three stackable sterling silver rings with hammered texture. Can be worn together or separately. Each ring is hand-forged and unique.',
                'category': 'jewelry',
                'price': 125.00,
                'quantity': 4,
                'ai_quality_score': 0.94,
                'materials': ['Sterling Silver'],
                'dimensions': 'Sizes 6, 7, 8 available',
                'weight': '0.2 lbs',
                'created_at': datetime.now(timezone.utc)
            },
            {
                'user_id': 'demo_artisan_5',
                'name': 'Tuscan Landscape Oil Painting',
                'description': 'Original oil painting of the rolling hills of Tuscany during golden hour. Painted en plein air using traditional techniques passed down through generations.',
                'category': 'paintings',
                'price': 450.00,
                'quantity': 1,
                'ai_quality_score': 0.97,
                'materials': ['Oil Paint', 'Canvas', 'Wooden Frame'],
                'dimensions': '16" x 20" (framed)',
                'weight': '3.0 lbs',
                'created_at': datetime.now(timezone.utc)
            },
            {
                'user_id': 'demo_artisan_2',
                'name': 'Live Edge Cutting Board',
                'description': 'Beautiful cutting board made from a single piece of maple with natural live edge. Food-safe finish and perfect for both cooking and serving cheese and charcuterie.',
                'category': 'woodwork',
                'price': 75.00,
                'quantity': 4,
                'ai_quality_score': 0.91,
                'materials': ['Maple Wood', 'Food-Safe Mineral Oil'],
                'dimensions': '16" L x 10" W x 1.5" H',
                'weight': '3.5 lbs',
                'created_at': datetime.now(timezone.utc)
            },
            {
                'user_id': 'demo_artisan_1',
                'name': 'Artisan Coffee Mug',
                'description': 'Hand-thrown coffee mug with a comfortable handle and beautiful blue-green glaze. Holds 12oz and keeps your coffee warm longer than mass-produced mugs.',
                'category': 'pottery',
                'price': 28.00,
                'quantity': 8,
                'ai_quality_score': 0.87,
                'materials': ['Porcelain', 'Celadon Glaze'],
                'dimensions': '4" H x 3.5" W',
                'weight': '0.8 lbs',
                'created_at': datetime.now(timezone.utc)
            },
            {
                'user_id': 'demo_artisan_4',
                'name': 'Copper Wind Chimes',
                'description': 'Handcrafted wind chimes made from recycled copper pipes. Creates beautiful, soothing tones in the breeze. Weather-resistant finish for outdoor use.',
                'category': 'metalwork',
                'price': 78.00,
                'quantity': 7,
                'ai_quality_score': 0.86,
                'materials': ['Recycled Copper', 'Hemp Cord'],
                'dimensions': '24" L total',
                'weight': '1.5 lbs',
                'created_at': datetime.now(timezone.utc)
            },
            {
                'user_id': 'demo_artisan_3',
                'name': 'Organic Cotton Throw Blanket',
                'description': 'Cozy throw blanket made from organic cotton and hand-dyed with natural indigo. Perfect for snuggling on the couch or adding a pop of color to your bedroom.',
                'category': 'textiles',
                'price': 145.00,
                'quantity': 3,
                'ai_quality_score': 0.90,
                'materials': ['Organic Cotton', 'Natural Indigo Dye'],
                'dimensions': '50" x 60"',
                'weight': '2.2 lbs',
                'created_at': datetime.now(timezone.utc)
            },
            {
                'user_id': 'demo_artisan_5',
                'name': 'Abstract Watercolor Series',
                'description': 'Set of three abstract watercolor paintings inspired by natural elements. Each piece flows into the next, creating a cohesive collection perfect for modern spaces.',
                'category': 'paintings',
                'price': 280.00,
                'quantity': 1,
                'ai_quality_score': 0.91,
                'materials': ['Watercolor Paint', 'Watercolor Paper'],
                'dimensions': '11" x 14" each (set of 3)',
                'weight': '1.2 lbs',
                'created_at': datetime.now(timezone.utc)
            }
        ]

        # Clear existing demo products first
        db.products.delete_many({'user_id': {'$regex': '^demo_artisan_'}})

        # Insert new products
        product_result = db.products.insert_many(sample_products)
        products_added = len(product_result.inserted_ids)

        # Step 2: Create and assign images
        uploads_dir = os.path.join('static', 'uploads')
        if not os.path.exists(uploads_dir):
            os.makedirs(uploads_dir, exist_ok=True)

        # Color schemes for different categories
        color_schemes = {
            'pottery': ('#8B4513', '#DEB887', '#F4A460'),  # Browns and sandy colors
            'woodwork': ('#654321', '#D2691E', '#DEB887'),  # Wood tones
            'textiles': ('#4B0082', '#9370DB', '#E6E6FA'),  # Purple/lavender
            'jewelry': ('#FFD700', '#FFA500', '#FFFACD'),  # Gold tones
            'paintings': ('#FF6347', '#FFB6C1', '#FFF8DC'),  # Artistic colors
            'metalwork': ('#708090', '#C0C0C0', '#F5F5F5'),  # Metal tones
        }

        images_created = 0
        products_updated = 0

        # Create images for each product
        for i, product in enumerate(sample_products):
            filename = f"demo_product_{i+1}.jpg"
            image_path = os.path.join(uploads_dir, filename)

            # Create image if it doesn't exist
            if not os.path.exists(image_path):
                try:
                    colors = color_schemes.get(product['category'], ('#696969', '#A9A9A9', '#D3D3D3'))

                    # Create image
                    img = Image.new('RGB', (400, 300), colors[2])
                    draw = ImageDraw.Draw(img)

                    # Add background pattern
                    for x in range(0, 400, 40):
                        for y in range(0, 300, 40):
                            if (x + y) % 80 == 0:
                                draw.rectangle([x, y, x+20, y+20], fill=colors[1])

                    # Add border
                    draw.rectangle([0, 0, 400, 10], fill=colors[0])
                    draw.rectangle([0, 0, 10, 300], fill=colors[0])
                    draw.rectangle([390, 0, 400, 300], fill=colors[0])
                    draw.rectangle([0, 290, 400, 300], fill=colors[0])

                    # Add text
                    try:
                        font = ImageFont.load_default()

                        # Product name (truncated if too long)
                        name = product['name']
                        if len(name) > 25:
                            name = name[:22] + "..."

                        # Draw text with shadow
                        draw.text((22, 120), name, fill='black', font=font)
                        draw.text((20, 118), name, fill='white', font=font)

                        # Category and price
                        category_text = f"{product['category'].title()}"
                        price_text = f"${product['price']}"

                        draw.text((22, 150), category_text, fill='black', font=font)
                        draw.text((20, 148), category_text, fill=colors[0], font=font)

                        draw.text((22, 170), price_text, fill='black', font=font)
                        draw.text((20, 168), price_text, fill='white', font=font)

                    except:
                        pass

                    # Save image
                    img.save(image_path, 'JPEG', quality=85)
                    images_created += 1

                except Exception as img_error:
                    continue

            # Update product with image path
            web_image_path = f"/static/uploads/{filename}"
            result = db.products.update_many(
                {'name': product['name']},
                {'$set': {'image_path': web_image_path}}
            )
            products_updated += result.modified_count

        return jsonify({
            'success': True,
            'message': f'Complete demo setup successful!',
            'products_added': products_added,
            'images_created': images_created,
            'products_updated': products_updated,
            'total_products': db.products.count_documents({}),
            'next_step': 'Visit /marketplace/ to see your products!'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Error setting up demo: {str(e)}'
        }), 500

@admin_bp.route('/create-images-only')
def create_images_only():
    """Simple route to just create sample images"""
    try:
        import os
        from PIL import Image, ImageDraw

        # Ensure uploads directory exists
        uploads_dir = os.path.join('static', 'uploads')
        try:
            if not os.path.exists(uploads_dir):
                os.makedirs(uploads_dir)
        except:
            pass  # Directory might already exist

        # Simple image data
        images_to_create = [
            ('demo_product_1.jpg', 'Ceramic Vase', 'pottery', '#8B4513'),
            ('demo_product_2.jpg', 'Jewelry Box', 'woodwork', '#654321'),
            ('demo_product_3.jpg', 'Silk Scarf', 'textiles', '#4B0082'),
            ('demo_product_4.jpg', 'Silver Rings', 'jewelry', '#FFD700'),
            ('demo_product_5.jpg', 'Oil Painting', 'paintings', '#FF6347'),
            ('demo_product_6.jpg', 'Cutting Board', 'woodwork', '#654321'),
            ('demo_product_7.jpg', 'Coffee Mug', 'pottery', '#8B4513'),
            ('demo_product_8.jpg', 'Wind Chimes', 'metalwork', '#708090'),
            ('demo_product_9.jpg', 'Throw Blanket', 'textiles', '#4B0082'),
            ('demo_product_10.jpg', 'Watercolor Art', 'paintings', '#FF6347'),
        ]

        created_count = 0

        for filename, name, category, color in images_to_create:
            image_path = os.path.join(uploads_dir, filename)

            try:
                # Create a simple colored image
                img = Image.new('RGB', (400, 300), color='lightblue')
                draw = ImageDraw.Draw(img)

                # Add colored rectangle
                draw.rectangle([50, 50, 350, 250], fill=color)

                # Add border
                draw.rectangle([45, 45, 355, 255], outline='black', width=3)

                # Add simple text
                draw.text((60, 120), name, fill='white')
                draw.text((60, 140), category.title(), fill='white')
                draw.text((60, 160), 'Handcrafted', fill='white')

                # Save the image
                img.save(image_path, 'JPEG', quality=85)
                created_count += 1

            except Exception as e:
                continue

        return jsonify({
            'success': True,
            'message': f'Created {created_count} sample images!',
            'images_created': created_count,
            'location': uploads_dir,
            'next_step': 'Check static/uploads folder and refresh marketplace'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Error creating images: {str(e)}'
        }), 500

@admin_bp.route('/test-image-creation')
def test_image_creation():
    """Ultra simple test to create one image"""
    try:
        import os
        from PIL import Image, ImageDraw

        # Direct path to uploads (we know it exists)
        uploads_dir = 'static/uploads'

        # Create just one test image
        img = Image.new('RGB', (400, 300), color='lightblue')
        draw = ImageDraw.Draw(img)
        draw.rectangle([50, 50, 350, 250], fill='red')
        draw.text((100, 150), 'TEST IMAGE', fill='white')

        # Save directly
        test_path = os.path.join(uploads_dir, 'test_image.jpg')
        img.save(test_path, 'JPEG')

        return jsonify({
            'success': True,
            'message': 'Test image created successfully!',
            'file_path': test_path,
            'check': 'Look in static/uploads for test_image.jpg'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Error: {str(e)}',
            'error_type': type(e).__name__
        }), 500

@admin_bp.route('/create-simple-images')
def create_simple_images():
    """Create simple text-based placeholder images without PIL"""
    try:
        import os

        # Product data
        products_data = [
            ('demo_product_1.jpg', 'Handcrafted Ceramic Vase', 'pottery', '$85'),
            ('demo_product_2.jpg', 'Walnut Jewelry Box', 'woodwork', '$185'),
            ('demo_product_3.jpg', 'Hand-Woven Silk Scarf', 'textiles', '$95'),
            ('demo_product_4.jpg', 'Sterling Silver Ring Set', 'jewelry', '$125'),
            ('demo_product_5.jpg', 'Tuscan Landscape Painting', 'paintings', '$450'),
            ('demo_product_6.jpg', 'Live Edge Cutting Board', 'woodwork', '$75'),
            ('demo_product_7.jpg', 'Artisan Coffee Mug', 'pottery', '$28'),
            ('demo_product_8.jpg', 'Copper Wind Chimes', 'metalwork', '$78'),
            ('demo_product_9.jpg', 'Cotton Throw Blanket', 'textiles', '$145'),
            ('demo_product_10.jpg', 'Watercolor Art Series', 'paintings', '$280'),
        ]

        # Create simple SVG images as text files
        uploads_dir = 'static/uploads'
        created_count = 0

        for filename, name, category, price in products_data:
            # Create SVG content
            svg_content = f'''<svg width="400" height="300" xmlns="http://www.w3.org/2000/svg">
  <rect width="400" height="300" fill="#f0f0f0"/>
  <rect x="20" y="20" width="360" height="260" fill="#e0e0e0" stroke="#333" stroke-width="2"/>
  <text x="200" y="120" text-anchor="middle" font-family="Arial" font-size="16" font-weight="bold">{name[:25]}</text>
  <text x="200" y="150" text-anchor="middle" font-family="Arial" font-size="14" fill="#666">{category.title()}</text>
  <text x="200" y="180" text-anchor="middle" font-family="Arial" font-size="18" font-weight="bold" fill="#2c5aa0">{price}</text>
  <text x="200" y="210" text-anchor="middle" font-family="Arial" font-size="12" fill="#888">Handcrafted Product</text>
</svg>'''

            # Save as SVG file (browsers can display these)
            svg_filename = filename.replace('.jpg', '.svg')
            file_path = os.path.join(uploads_dir, svg_filename)

            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(svg_content)

            created_count += 1

        # Update products in database to use SVG images
        db = get_db()
        updated_count = 0

        for i, (filename, name, category, price) in enumerate(products_data):
            svg_filename = filename.replace('.jpg', '.svg')
            web_path = f"/static/uploads/{svg_filename}"

            # Update products that match
            result = db.products.update_many(
                {'name': {'$regex': name.split()[0], '$options': 'i'}},
                {'$set': {'image_path': web_path}}
            )
            updated_count += result.modified_count

        return jsonify({
            'success': True,
            'message': f'Created {created_count} SVG placeholder images!',
            'images_created': created_count,
            'products_updated': updated_count,
            'note': 'Using SVG images instead of JPEG (no PIL required)',
            'next_step': 'Refresh marketplace to see images'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Error creating SVG images: {str(e)}',
            'error_type': type(e).__name__
        }), 500

@admin_bp.route('/scan-uploaded-images')
def scan_uploaded_images():
    """Scan static/uploads for real images and assign them to products"""
    try:
        import os
        import glob

        db = get_db()
        uploads_dir = 'static/uploads'

        # Find all image files in uploads directory
        image_extensions = ['*.jpg', '*.jpeg', '*.png', '*.gif', '*.webp']
        found_images = []

        for ext in image_extensions:
            found_images.extend(glob.glob(os.path.join(uploads_dir, ext)))
            found_images.extend(glob.glob(os.path.join(uploads_dir, ext.upper())))

        # Get all products without images
        products_without_images = list(db.products.find({
            '$or': [
                {'image_path': {'$exists': False}},
                {'image_path': None},
                {'image_path': ''}
            ]
        }))

        # Assign images to products
        assignments = []
        updated_count = 0

        for i, product in enumerate(products_without_images):
            if i < len(found_images):
                image_file = found_images[i]
                # Convert to web path
                web_path = '/' + image_file.replace('\\', '/')

                # Update product
                result = db.products.update_one(
                    {'_id': product['_id']},
                    {'$set': {'image_path': web_path}}
                )

                if result.modified_count > 0:
                    updated_count += 1
                    assignments.append({
                        'product': product['name'],
                        'image': os.path.basename(image_file)
                    })

        return jsonify({
            'success': True,
            'message': f'Scanned and assigned {updated_count} images to products',
            'found_images': len(found_images),
            'products_without_images': len(products_without_images),
            'assignments': assignments[:10],  # Show first 10
            'instructions': {
                'step1': 'Add your real images to static/uploads/ folder',
                'step2': 'Visit this route again to auto-assign them',
                'step3': 'Supported formats: JPG, PNG, GIF, WebP'
            }
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Error scanning images: {str(e)}'
        }), 500
