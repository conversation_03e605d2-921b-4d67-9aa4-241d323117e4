from pymongo import MongoClient
from flask import current_app

def get_db():
    """Get database connection"""
    client = MongoClient(current_app.config['MONGODB_URI'])
    return client.craftconnect

def init_db():
    """Initialize database with collections"""
    db = get_db()
    
    # Create collections if they don't exist
    collections = ['users', 'products', 'orders', 'analytics', 'iot_data']
    for collection in collections:
        if collection not in db.list_collection_names():
            db.create_collection(collection)
    
    print("Database initialized successfully")
