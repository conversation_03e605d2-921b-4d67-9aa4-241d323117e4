from flask import Blueprint, render_template, session, redirect, url_for, jsonify
import random
from datetime import datetime, timezone

iot_bp = Blueprint('iot', __name__, url_prefix='/iot')

@iot_bp.before_request
def require_login():
    if 'user_id' not in session:
        return redirect(url_for('auth.login'))

@iot_bp.route('/')
def iot_dashboard():
    return render_template('iot/dashboard.html')

@iot_bp.route('/api/sensor-data')
def sensor_data():
    # Simulate IoT sensor data
    data = {
        'timestamp': datetime.now(timezone.utc).isoformat(),
        'temperature': round(random.uniform(18, 28), 1),
        'humidity': round(random.uniform(40, 70), 1),
        'air_quality': round(random.uniform(50, 100), 1),
        'noise_level': round(random.uniform(30, 80), 1),
        'light_intensity': round(random.uniform(200, 800), 1),
        'workshop_status': random.choice(['active', 'idle', 'maintenance'])
    }
    return jsonify(data)
