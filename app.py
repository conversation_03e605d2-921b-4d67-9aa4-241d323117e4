# app.py
import os
# Suppress TensorFlow warnings
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'
os.environ['TF_ENABLE_ONEDNN_OPTS'] = '0'

import matplotlib
matplotlib.use('Agg')

from flask import Flask, render_template, jsonify
from database import init_db

# Import blueprints with error handling
# Initialize blueprint variables to None first
auth_bp = upload_bp = dashboard_bp = admin_bp = marketplace_bp = artisan_bp = analytics_bp = iot_bp = ai_bp = None

try:
    from routes.auth import auth_bp
    from routes.upload import upload_bp
    from routes.dashboard import dashboard_bp
    from routes.admin import admin_bp
    from routes.marketplace import marketplace_bp
    from routes.artisan import artisan_bp
    from routes.analytics import analytics_bp
    from routes.iot_simulation import iot_bp
    from ai_services import ai_bp
except ImportError as e:
    print(f"Warning: Could not import blueprint: {e}")

app = Flask(__name__)
app.secret_key = 'artisan_heritage_platform_2024'
app.config['MONGODB_URI'] = 'mongodb://localhost:27017/craftconnect'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

# Initialize database
with app.app_context():
    init_db()

# Register blueprints with error handling
blueprints = [
    ('auth_bp', auth_bp),
    ('upload_bp', upload_bp),
    ('dashboard_bp', dashboard_bp),
    ('admin_bp', admin_bp),
    ('marketplace_bp', marketplace_bp),
    ('artisan_bp', artisan_bp),
    ('analytics_bp', analytics_bp),
    ('iot_bp', iot_bp),
    ('ai_bp', ai_bp)
]

for name, bp in blueprints:
    if bp is not None:
        try:
            app.register_blueprint(bp)
            print(f"✓ Registered {name}")
        except Exception as e:
            print(f"✗ Error registering {name}: {e}")
    else:
        print(f"✗ Skipped {name} (not imported)")

@app.route('/')
def home():
    return render_template('home.html')

@app.route('/about')
def about():
    return render_template('about.html')

@app.errorhandler(413)
def too_large(e):
    return jsonify({'error': 'File too large. Maximum size is 16MB.'}), 413

@app.errorhandler(404)
def not_found(e):
    return render_template('404.html'), 404

@app.errorhandler(500)
def server_error(e):
    return jsonify({'error': 'Internal server error'}), 500

@app.errorhandler(Exception)
def handle_exception(e):
    """Handle all unhandled exceptions"""
    app.logger.error(f"Unhandled exception: {str(e)}")
    return jsonify({'error': 'An unexpected error occurred'}), 500

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)



