{% extends "base.html" %}

{% block title %}Dashboard - CraftConnect{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-md-12">
            <h1 class="mb-4">Dashboard</h1>
            
            <!-- Stats Cards -->
            <div class="row mb-4">
                <div class="col-md-4">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4>{{ stats.total_products }}</h4>
                                    <p class="mb-0">Total Products</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-box fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4>{{ stats.total_orders }}</h4>
                                    <p class="mb-0">Total Orders</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-shopping-cart fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4>${{ stats.total_revenue }}</h4>
                                    <p class="mb-0">Total Revenue</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-dollar-sign fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Quick Actions -->
            <div class="row mb-4">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Quick Actions</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3">
                                    <a href="{{ url_for('upload.upload_product') }}" class="btn btn-primary btn-block">
                                        <i class="fas fa-plus"></i> Add Product
                                    </a>
                                </div>
                                <div class="col-md-3">
                                    <a href="{{ url_for('marketplace.marketplace') }}" class="btn btn-success btn-block">
                                        <i class="fas fa-store"></i> View Marketplace
                                    </a>
                                </div>
                                <div class="col-md-3">
                                    <a href="{{ url_for('analytics.analytics') }}" class="btn btn-info btn-block">
                                        <i class="fas fa-chart-bar"></i> Analytics
                                    </a>
                                </div>
                                <div class="col-md-3">
                                    <a href="{{ url_for('iot.iot_dashboard') }}" class="btn btn-warning btn-block">
                                        <i class="fas fa-wifi"></i> IoT Dashboard
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Recent Activity -->
            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Recent Products</h5>
                        </div>
                        <div class="card-body">
                            <div id="recentProducts">
                                <p class="text-muted">Loading recent products...</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Recent Orders</h5>
                        </div>
                        <div class="card-body">
                            <div id="recentOrders">
                                <p class="text-muted">Loading recent orders...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Load dashboard data
document.addEventListener('DOMContentLoaded', function() {
    loadRecentData();
});

async function loadRecentData() {
    try {
        const response = await fetch('/dashboard/api/stats');
        const data = await response.json();
        
        // Update recent products
        if (data.recent_products && data.recent_products.length > 0) {
            const productsHtml = data.recent_products.map(product => `
                <div class="mb-2">
                    <strong>${product.name}</strong><br>
                    <small class="text-muted">${product.category} - $${product.price}</small>
                </div>
            `).join('');
            document.getElementById('recentProducts').innerHTML = productsHtml;
        } else {
            document.getElementById('recentProducts').innerHTML = '<p class="text-muted">No products yet.</p>';
        }
        
        // Update recent orders
        if (data.recent_orders && data.recent_orders.length > 0) {
            const ordersHtml = data.recent_orders.map(order => `
                <div class="mb-2">
                    <strong>Order #${order.id}</strong><br>
                    <small class="text-muted">$${order.total} - ${order.status}</small>
                </div>
            `).join('');
            document.getElementById('recentOrders').innerHTML = ordersHtml;
        } else {
            document.getElementById('recentOrders').innerHTML = '<p class="text-muted">No orders yet.</p>';
        }
    } catch (error) {
        console.error('Error loading dashboard data:', error);
    }
}
</script>
{% endblock %}
