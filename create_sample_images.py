#!/usr/bin/env python3
"""
Simple script to create sample product images
"""

import os
from PIL import Image, ImageDraw, ImageFont

def create_product_image(name, category, filename, size=(400, 300)):
    """Create a sample product image"""
    
    # Color schemes for different categories
    colors = {
        'pottery': ('#8B4513', '#DEB887'),  # Brown/tan
        'woodwork': ('#654321', '#D2691E'),  # Dark brown/orange
        'textiles': ('#4B0082', '#9370DB'),  # Purple
        'jewelry': ('#FFD700', '#FFA500'),  # Gold/orange
        'paintings': ('#FF6347', '#FFB6C1'),  # Red/pink
        'metalwork': ('#708090', '#C0C0C0'),  # Gray/silver
    }
    
    bg_color, text_color = colors.get(category, ('#696969', '#A9A9A9'))
    
    # Create image
    img = Image.new('RGB', size, bg_color)
    draw = ImageDraw.Draw(img)
    
    # Add decorative border
    border = 10
    draw.rectangle([border, border, size[0]-border, size[1]-border], 
                  outline=text_color, width=3)
    
    # Add text
    try:
        font = ImageFont.load_default()
    except:
        font = None
    
    # Product name
    text_lines = [name, f"Category: {category.title()}", "Handcrafted with Care"]
    
    y_start = size[1] // 2 - 30
    for i, line in enumerate(text_lines):
        if font:
            bbox = draw.textbbox((0, 0), line, font=font)
            text_width = bbox[2] - bbox[0]
        else:
            text_width = len(line) * 6  # Rough estimate
        
        x = (size[0] - text_width) // 2
        y = y_start + i * 20
        
        # Text shadow
        draw.text((x+1, y+1), line, fill='black', font=font)
        draw.text((x, y), line, fill='white', font=font)
    
    return img

def main():
    """Create sample images for products"""
    
    # Ensure uploads directory exists
    uploads_dir = os.path.join('static', 'uploads')
    os.makedirs(uploads_dir, exist_ok=True)
    
    # Sample products
    products = [
        ('Handcrafted Ceramic Vase', 'pottery', 'ceramic_vase.jpg'),
        ('Walnut Jewelry Box', 'woodwork', 'jewelry_box.jpg'),
        ('Hand-Woven Silk Scarf', 'textiles', 'silk_scarf.jpg'),
        ('Sterling Silver Rings', 'jewelry', 'silver_rings.jpg'),
        ('Tuscan Landscape', 'paintings', 'landscape_painting.jpg'),
        ('Live Edge Board', 'woodwork', 'cutting_board.jpg'),
        ('Artisan Coffee Mug', 'pottery', 'coffee_mug.jpg'),
        ('Copper Wind Chimes', 'metalwork', 'wind_chimes.jpg'),
        ('Cotton Throw Blanket', 'textiles', 'throw_blanket.jpg'),
        ('Watercolor Series', 'paintings', 'watercolor_art.jpg'),
    ]
    
    print("🎨 Creating sample product images...")
    
    for name, category, filename in products:
        try:
            img = create_product_image(name, category, filename)
            image_path = os.path.join(uploads_dir, filename)
            img.save(image_path, 'JPEG', quality=85)
            print(f"✓ Created: {filename}")
        except Exception as e:
            print(f"✗ Error creating {filename}: {e}")
    
    print(f"\n✅ Sample images created in {uploads_dir}/")
    print("🌐 Access admin route to update products: http://127.0.0.1:5000/admin/add-product-images")

if __name__ == "__main__":
    main()
