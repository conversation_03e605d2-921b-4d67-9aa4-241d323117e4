{% extends "base.html" %}

{% block title %}IoT Dashboard - CraftConnect{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-md-12">
            <h1 class="mb-4">IoT Workshop Monitoring</h1>
            <p class="lead">Real-time monitoring of your workshop environment and equipment.</p>
            
            <!-- Status Cards -->
            <div class="row mb-4">
                <div class="col-md-2">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fas fa-thermometer-half fa-2x text-danger mb-2"></i>
                            <h5 id="temperature">--°C</h5>
                            <small class="text-muted">Temperature</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fas fa-tint fa-2x text-info mb-2"></i>
                            <h5 id="humidity">--%</h5>
                            <small class="text-muted">Humidity</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fas fa-wind fa-2x text-success mb-2"></i>
                            <h5 id="airQuality">--</h5>
                            <small class="text-muted">Air Quality</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fas fa-volume-up fa-2x text-warning mb-2"></i>
                            <h5 id="noiseLevel">-- dB</h5>
                            <small class="text-muted">Noise Level</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fas fa-lightbulb fa-2x text-primary mb-2"></i>
                            <h5 id="lightIntensity">-- lux</h5>
                            <small class="text-muted">Light</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fas fa-cog fa-2x mb-2" id="statusIcon"></i>
                            <h5 id="workshopStatus">--</h5>
                            <small class="text-muted">Status</small>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Charts -->
            <div class="row mb-4">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">Environmental Trends</h5>
                            <div class="btn-group btn-group-sm" role="group">
                                <button type="button" class="btn btn-outline-primary active" onclick="showChart('temperature')">Temperature</button>
                                <button type="button" class="btn btn-outline-primary" onclick="showChart('humidity')">Humidity</button>
                                <button type="button" class="btn btn-outline-primary" onclick="showChart('airQuality')">Air Quality</button>
                            </div>
                        </div>
                        <div class="card-body">
                            <canvas id="environmentChart" width="400" height="200"></canvas>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Workshop Activity</h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <div class="d-flex justify-content-between">
                                    <span>Active Time Today</span>
                                    <strong>6h 45m</strong>
                                </div>
                                <div class="progress mt-1">
                                    <div class="progress-bar bg-success" style="width: 84%"></div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <div class="d-flex justify-content-between">
                                    <span>Equipment Efficiency</span>
                                    <strong>92%</strong>
                                </div>
                                <div class="progress mt-1">
                                    <div class="progress-bar bg-info" style="width: 92%"></div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <div class="d-flex justify-content-between">
                                    <span>Energy Usage</span>
                                    <strong>78%</strong>
                                </div>
                                <div class="progress mt-1">
                                    <div class="progress-bar bg-warning" style="width: 78%"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Alerts and Notifications -->
            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Recent Alerts</h5>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-warning alert-dismissible fade show" role="alert">
                                <i class="fas fa-exclamation-triangle"></i>
                                <strong>High Humidity Detected</strong><br>
                                Workshop humidity is above optimal range (72%). Consider ventilation.
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                            <div class="alert alert-info alert-dismissible fade show" role="alert">
                                <i class="fas fa-info-circle"></i>
                                <strong>Equipment Maintenance Due</strong><br>
                                Pottery wheel #2 is due for scheduled maintenance in 3 days.
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Connected Devices</h5>
                        </div>
                        <div class="card-body">
                            <div class="list-group list-group-flush">
                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                    <div>
                                        <i class="fas fa-thermometer-half text-danger"></i>
                                        Temperature Sensor #1
                                    </div>
                                    <span class="badge bg-success rounded-pill">Online</span>
                                </div>
                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                    <div>
                                        <i class="fas fa-tint text-info"></i>
                                        Humidity Sensor #1
                                    </div>
                                    <span class="badge bg-success rounded-pill">Online</span>
                                </div>
                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                    <div>
                                        <i class="fas fa-wind text-success"></i>
                                        Air Quality Monitor
                                    </div>
                                    <span class="badge bg-success rounded-pill">Online</span>
                                </div>
                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                    <div>
                                        <i class="fas fa-camera text-primary"></i>
                                        Security Camera #1
                                    </div>
                                    <span class="badge bg-warning rounded-pill">Offline</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
let environmentChart;
let sensorData = [];
let currentMetric = 'temperature';

document.addEventListener('DOMContentLoaded', function() {
    initializeChart();
    updateSensorData();
    
    // Update data every 5 seconds
    setInterval(updateSensorData, 5000);
});

function initializeChart() {
    const ctx = document.getElementById('environmentChart').getContext('2d');
    environmentChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: [],
            datasets: [{
                label: 'Temperature (°C)',
                data: [],
                borderColor: 'rgb(255, 99, 132)',
                backgroundColor: 'rgba(255, 99, 132, 0.2)',
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: false
                }
            },
            plugins: {
                legend: {
                    display: true
                }
            }
        }
    });
}

async function updateSensorData() {
    try {
        const response = await fetch('/iot/api/sensor-data');
        const data = await response.json();
        
        // Update display values
        document.getElementById('temperature').textContent = data.temperature + '°C';
        document.getElementById('humidity').textContent = data.humidity + '%';
        document.getElementById('airQuality').textContent = data.air_quality;
        document.getElementById('noiseLevel').textContent = data.noise_level + ' dB';
        document.getElementById('lightIntensity').textContent = data.light_intensity + ' lux';
        
        // Update workshop status
        const statusElement = document.getElementById('workshopStatus');
        const statusIcon = document.getElementById('statusIcon');
        statusElement.textContent = data.workshop_status.charAt(0).toUpperCase() + data.workshop_status.slice(1);
        
        // Update status icon color
        statusIcon.className = 'fas fa-cog fa-2x mb-2 ';
        switch(data.workshop_status) {
            case 'active':
                statusIcon.className += 'text-success';
                break;
            case 'idle':
                statusIcon.className += 'text-warning';
                break;
            case 'maintenance':
                statusIcon.className += 'text-danger';
                break;
        }
        
        // Store data for chart
        sensorData.push(data);
        if (sensorData.length > 20) {
            sensorData.shift(); // Keep only last 20 data points
        }
        
        updateChart();
        
    } catch (error) {
        console.error('Error fetching sensor data:', error);
    }
}

function updateChart() {
    const labels = sensorData.map((_, index) => {
        const now = new Date();
        now.setSeconds(now.getSeconds() - (sensorData.length - 1 - index) * 5);
        return now.toLocaleTimeString();
    });
    
    const data = sensorData.map(item => item[currentMetric]);
    
    environmentChart.data.labels = labels;
    environmentChart.data.datasets[0].data = data;
    environmentChart.update('none');
}

function showChart(metric) {
    currentMetric = metric;
    
    // Update button states
    document.querySelectorAll('.btn-group button').forEach(btn => {
        btn.classList.remove('active');
    });
    event.target.classList.add('active');
    
    // Update chart
    const labels = {
        temperature: 'Temperature (°C)',
        humidity: 'Humidity (%)',
        airQuality: 'Air Quality Index'
    };
    
    const colors = {
        temperature: 'rgb(255, 99, 132)',
        humidity: 'rgb(54, 162, 235)',
        airQuality: 'rgb(75, 192, 192)'
    };
    
    environmentChart.data.datasets[0].label = labels[metric];
    environmentChart.data.datasets[0].borderColor = colors[metric];
    environmentChart.data.datasets[0].backgroundColor = colors[metric].replace('rgb', 'rgba').replace(')', ', 0.2)');
    
    updateChart();
}
</script>
{% endblock %}
