# This file makes the database directory a Python package
from .db import get_db
from .models import init_mongodb_indexes

def init_db():
    """Initialize database with collections and indexes"""
    try:
        db = get_db()

        # Create collections if they don't exist
        collections = ['users', 'artisans', 'products', 'orders', 'analytics', 'iot_data']
        for collection in collections:
            if collection not in db.list_collection_names():
                db.create_collection(collection)

        # Initialize indexes
        init_mongodb_indexes(db)

        print("✓ Database initialized successfully")
        return True
    except Exception as e:
        print(f"✗ Database initialization failed: {e}")
        return False