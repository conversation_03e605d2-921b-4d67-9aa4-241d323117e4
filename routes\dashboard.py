from flask import Blueprint, render_template, session, redirect, url_for, jsonify
from database import get_db

dashboard_bp = Blueprint('dashboard', __name__, url_prefix='/dashboard')

@dashboard_bp.before_request
def require_login():
    if 'user_id' not in session:
        return redirect(url_for('auth.login'))

@dashboard_bp.route('/')
def index():
    # Dashboard route - fixed URL reference issue
    db = get_db()
    user_id = session['user_id']
    
    # Get user stats
    stats = {
        'total_products': db.products.count_documents({'user_id': user_id}),
        'total_orders': db.orders.count_documents({'user_id': user_id}),
        'total_revenue': 0  # Calculate from orders
    }
    
    return render_template('dashboard/index.html', stats=stats)

@dashboard_bp.route('/api/stats')
def api_stats():
    db = get_db()
    user_id = session['user_id']
    
    stats = {
        'products': db.products.count_documents({'user_id': user_id}),
        'orders': db.orders.count_documents({'user_id': user_id}),
        'revenue': 0
    }
    
    return jsonify(stats)
