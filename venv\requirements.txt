# Core Flask Framework
flask==3.1.1
flask-cors==4.0.0

# Database
pymongo==4.13.2

# AI/ML Libraries (PyTorch instead of TensorFlow)
torch>=2.0.0
torchvision>=0.15.0
torchaudio>=2.0.0
scikit-learn>=1.3.0
joblib>=1.3.0
numpy>=1.26.0
scipy==1.11.4

# Image Processing
opencv-python==********
scikit-image==0.22.0
pillow>=11.3.0

# Data Processing & Analytics
pandas==2.1.4
matplotlib==3.8.2
seaborn==0.13.0

# Web Security & Authentication
werkzeug>=3.1.0
cryptography>=43.0.0
bcrypt>=4.0.0

# HTTP & API
requests>=2.31.0
urllib3>=2.0.0

# Configuration & Environment
python-dotenv==1.0.0

# Production Server
gunicorn==21.2.0

# File Handling & Utilities
python-magic>=0.4.27
pathlib2>=2.3.7

# Email Services
flask-mail>=0.9.1

# Form Handling & Validation
wtforms>=3.0.0
flask-wtf>=1.1.0

# Date & Time Utilities
python-dateutil>=2.8.0

# JSON & Data Serialization
jsonschema>=4.17.0

# Development Tools
pytest==7.4.3
pytest-cov==4.1.0
black==23.12.1
flake8==6.1.0

# Optional: Web Scraping & HTML Processing
beautifulsoup4==4.12.2
lxml==4.9.4

# Optional: Advanced Features
redis>=4.5.0
celery>=5.3.0

# Optional: Payment Processing
stripe>=5.4.0

# Optional: Cloud Storage
boto3>=1.26.0

# Optional: Caching
flask-caching>=2.0.0

# Optional: Rate Limiting
flask-limiter>=3.3.0

# Optional: API Documentation
flask-restx>=1.1.0

# Optional: Monitoring
sentry-sdk[flask]>=1.25.0
