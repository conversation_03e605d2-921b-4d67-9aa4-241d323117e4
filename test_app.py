#!/usr/bin/env python3
"""
Test script for CraftConnect application
"""

import requests
import json
import sys

def test_application():
    """Test the CraftConnect application endpoints"""
    base_url = "http://127.0.0.1:5000"
    
    print("🧪 Testing CraftConnect Application")
    print("=" * 50)
    
    tests = [
        {
            "name": "Home Page",
            "url": f"{base_url}/",
            "expected_status": 200
        },
        {
            "name": "About Page", 
            "url": f"{base_url}/about",
            "expected_status": 200
        },
        {
            "name": "Login Page",
            "url": f"{base_url}/auth/login",
            "expected_status": 200
        },
        {
            "name": "Register Page",
            "url": f"{base_url}/auth/register", 
            "expected_status": 200
        },
        {
            "name": "Marketplace",
            "url": f"{base_url}/marketplace/",
            "expected_status": 200
        },
        {
            "name": "Marketplace API",
            "url": f"{base_url}/marketplace/api/products",
            "expected_status": 200
        },
        {
            "name": "IoT Sensor Data API",
            "url": f"{base_url}/iot/api/sensor-data",
            "expected_status": 302  # Redirect to login
        }
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            response = requests.get(test["url"], timeout=5, allow_redirects=False)
            
            if response.status_code == test["expected_status"]:
                print(f"✅ {test['name']}: PASS (Status: {response.status_code})")
                passed += 1
            else:
                print(f"❌ {test['name']}: FAIL (Expected: {test['expected_status']}, Got: {response.status_code})")
                failed += 1
                
        except requests.exceptions.RequestException as e:
            print(f"❌ {test['name']}: ERROR - {str(e)}")
            failed += 1
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 All tests passed! Application is working correctly.")
        return True
    else:
        print("⚠️  Some tests failed. Check the application logs.")
        return False

def test_database_connection():
    """Test database connectivity"""
    print("\n🗄️  Testing Database Connection")
    print("-" * 30)
    
    try:
        from pymongo import MongoClient
        client = MongoClient('mongodb://localhost:27017/')
        db = client.craftconnect
        
        # Test connection
        collections = db.list_collection_names()
        print(f"✅ MongoDB connection successful")
        print(f"📁 Available collections: {', '.join(collections)}")
        
        # Test basic operations
        test_doc = {"test": "connection", "timestamp": "2024-01-01"}
        result = db.test_collection.insert_one(test_doc)
        db.test_collection.delete_one({"_id": result.inserted_id})
        print("✅ Database read/write operations working")
        
        return True
        
    except Exception as e:
        print(f"❌ Database connection failed: {str(e)}")
        return False

def test_ai_services():
    """Test AI services availability"""
    print("\n🤖 Testing AI Services")
    print("-" * 25)
    
    try:
        import torch
        import torchvision
        print(f"✅ PyTorch version: {torch.__version__}")
        print(f"✅ TorchVision version: {torchvision.__version__}")
        print(f"✅ CUDA available: {torch.cuda.is_available()}")
        
        # Test basic tensor operations
        x = torch.randn(2, 3)
        y = x + 1
        print("✅ Basic tensor operations working")
        
        return True
        
    except ImportError as e:
        print(f"❌ AI services not available: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ AI services error: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 CraftConnect Application Test Suite")
    print("=" * 60)
    
    # Test database
    db_ok = test_database_connection()
    
    # Test AI services
    ai_ok = test_ai_services()
    
    # Test web application
    app_ok = test_application()
    
    print("\n" + "=" * 60)
    print("📋 FINAL SUMMARY")
    print("=" * 60)
    print(f"Database: {'✅ OK' if db_ok else '❌ FAILED'}")
    print(f"AI Services: {'✅ OK' if ai_ok else '❌ FAILED'}")
    print(f"Web Application: {'✅ OK' if app_ok else '❌ FAILED'}")
    
    if db_ok and ai_ok and app_ok:
        print("\n🎉 ALL SYSTEMS OPERATIONAL!")
        print("🌐 Application is running at: http://127.0.0.1:5000")
        sys.exit(0)
    else:
        print("\n⚠️  Some components have issues. Check the logs above.")
        sys.exit(1)
