from flask import Blueprint, render_template, jsonify
from database import get_db

marketplace_bp = Blueprint('marketplace', __name__, url_prefix='/marketplace')

@marketplace_bp.route('/')
def marketplace():
    return render_template('marketplace/index.html')

@marketplace_bp.route('/api/products')
def api_products():
    db = get_db()
    products = list(db.products.find().limit(20))

    # Convert ObjectId to string for JSON serialization
    for product in products:
        product['_id'] = str(product['_id'])

    return jsonify(products)

