#!/usr/bin/env python3
"""
Quick script to add sample products directly to MongoDB
"""

from pymongo import MongoClient
from datetime import datetime, timezone
import sys

def add_products():
    try:
        # Connect to MongoDB
        client = MongoClient('mongodb://localhost:27017/')
        db = client.craftconnect
        
        # Sample products
        products = [
            {
                'user_id': 'sample_user_1',
                'name': 'Handcrafted Ceramic Vase',
                'description': 'Beautiful ceramic vase with traditional Southwest patterns. Hand-thrown on the wheel and fired in a wood kiln.',
                'category': 'pottery',
                'price': 85.00,
                'quantity': 3,
                'ai_quality_score': 0.92,
                'created_at': datetime.now(timezone.utc)
            },
            {
                'user_id': 'sample_user_2',
                'name': 'Handcrafted Walnut Jewelry Box',
                'description': 'Elegant jewelry box made from sustainably sourced black walnut with traditional dovetail joints.',
                'category': 'woodwork',
                'price': 185.00,
                'quantity': 1,
                'ai_quality_score': 0.95,
                'created_at': datetime.now(timezone.utc)
            },
            {
                'user_id': 'sample_user_3',
                'name': 'Hand-Woven Silk Scarf',
                'description': 'Luxurious silk scarf hand-woven on a traditional loom with intricate geometric patterns.',
                'category': 'textiles',
                'price': 95.00,
                'quantity': 5,
                'ai_quality_score': 0.93,
                'created_at': datetime.now(timezone.utc)
            },
            {
                'user_id': 'sample_user_4',
                'name': 'Sterling Silver Ring Set',
                'description': 'Set of three stackable sterling silver rings with hammered texture. Hand-forged and unique.',
                'category': 'jewelry',
                'price': 125.00,
                'quantity': 4,
                'ai_quality_score': 0.94,
                'created_at': datetime.now(timezone.utc)
            },
            {
                'user_id': 'sample_user_5',
                'name': 'Tuscan Landscape Oil Painting',
                'description': 'Original oil painting of rolling Tuscan hills during golden hour. Painted en plein air.',
                'category': 'paintings',
                'price': 450.00,
                'quantity': 1,
                'ai_quality_score': 0.97,
                'created_at': datetime.now(timezone.utc)
            },
            {
                'user_id': 'sample_user_2',
                'name': 'Live Edge Cutting Board',
                'description': 'Beautiful cutting board made from a single piece of maple with natural live edge.',
                'category': 'woodwork',
                'price': 75.00,
                'quantity': 4,
                'ai_quality_score': 0.91,
                'created_at': datetime.now(timezone.utc)
            },
            {
                'user_id': 'sample_user_1',
                'name': 'Artisan Coffee Mug',
                'description': 'Hand-thrown coffee mug with comfortable handle and beautiful blue-green glaze.',
                'category': 'pottery',
                'price': 28.00,
                'quantity': 8,
                'ai_quality_score': 0.87,
                'created_at': datetime.now(timezone.utc)
            },
            {
                'user_id': 'sample_user_4',
                'name': 'Copper Wind Chimes',
                'description': 'Handcrafted wind chimes made from recycled copper pipes with beautiful, soothing tones.',
                'category': 'metalwork',
                'price': 78.00,
                'quantity': 7,
                'ai_quality_score': 0.86,
                'created_at': datetime.now(timezone.utc)
            },
            {
                'user_id': 'sample_user_3',
                'name': 'Organic Cotton Throw Blanket',
                'description': 'Cozy throw blanket made from organic cotton and hand-dyed with natural indigo.',
                'category': 'textiles',
                'price': 145.00,
                'quantity': 3,
                'ai_quality_score': 0.90,
                'created_at': datetime.now(timezone.utc)
            },
            {
                'user_id': 'sample_user_5',
                'name': 'Abstract Watercolor Series',
                'description': 'Set of three abstract watercolor paintings inspired by natural elements.',
                'category': 'paintings',
                'price': 280.00,
                'quantity': 1,
                'ai_quality_score': 0.91,
                'created_at': datetime.now(timezone.utc)
            }
        ]
        
        # Insert products
        result = db.products.insert_many(products)
        print(f"✅ Successfully added {len(result.inserted_ids)} products to the database!")
        
        # Show summary
        print("\n📊 Product Summary:")
        for category in ['pottery', 'woodwork', 'textiles', 'jewelry', 'paintings', 'metalwork']:
            count = db.products.count_documents({'category': category})
            if count > 0:
                print(f"  {category.title()}: {count} products")
        
        total = db.products.count_documents({})
        print(f"\n🛍️ Total products in database: {total}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False

if __name__ == "__main__":
    print("🎨 Adding Sample Products to CraftConnect")
    print("=" * 40)
    
    if add_products():
        print("\n🌐 View products at: http://127.0.0.1:5000/marketplace/")
    else:
        sys.exit(1)
