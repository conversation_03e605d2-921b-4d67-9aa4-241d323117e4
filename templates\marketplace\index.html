{% extends "base.html" %}

{% block title %}Marketplace - CraftConnect{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <h1 class="mb-4">Artisan Marketplace</h1>
            <p class="lead">Discover authentic handcrafted products from talented artisans around the world.</p>
            
            <!-- Search and Filter -->
            <div class="row mb-4">
                <div class="col-md-8">
                    <div class="input-group">
                        <input type="text" class="form-control" id="searchInput" placeholder="Search products...">
                        <button class="btn btn-outline-secondary" type="button" id="searchBtn" aria-label="Search products">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
                <div class="col-md-4">
                    <select class="form-select" id="categoryFilter" aria-label="Filter products by category">
                        <option value="">All Categories</option>
                        <option value="pottery">Pottery</option>
                        <option value="textiles">Textiles</option>
                        <option value="woodwork">Woodwork</option>
                        <option value="metalwork">Metalwork</option>
                        <option value="jewelry">Jewelry</option>
                        <option value="paintings">Paintings</option>
                    </select>
                </div>
            </div>
            
            <!-- Products Grid -->
            <div class="row" id="productsGrid">
                <div class="col-12 text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2">Loading products...</p>
                </div>
            </div>
            
            <!-- No Products Message -->
            <div class="row d-none" id="noProducts">
                <div class="col-12 text-center">
                    <div class="card">
                        <div class="card-body">
                            <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
                            <h4>No Products Found</h4>
                            <p class="text-muted">Be the first to add a product to the marketplace!</p>
                            <a href="{{ url_for('upload.upload_product') }}" class="btn btn-primary">Add Product</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
let allProducts = [];

document.addEventListener('DOMContentLoaded', function() {
    loadProducts();
    
    // Search functionality
    document.getElementById('searchBtn').addEventListener('click', filterProducts);
    document.getElementById('searchInput').addEventListener('keyup', function(e) {
        if (e.key === 'Enter') {
            filterProducts();
        }
    });
    
    // Category filter
    document.getElementById('categoryFilter').addEventListener('change', filterProducts);
});

async function loadProducts() {
    try {
        const response = await fetch('/marketplace/api/products');
        allProducts = await response.json();
        displayProducts(allProducts);
    } catch (error) {
        console.error('Error loading products:', error);
        document.getElementById('productsGrid').innerHTML = `
            <div class="col-12 text-center">
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle"></i>
                    Error loading products. Please try again later.
                </div>
            </div>
        `;
    }
}

function displayProducts(products) {
    const grid = document.getElementById('productsGrid');
    const noProducts = document.getElementById('noProducts');
    
    if (products.length === 0) {
        grid.classList.add('d-none');
        noProducts.classList.remove('d-none');
        return;
    }
    
    grid.classList.remove('d-none');
    noProducts.classList.add('d-none');
    
    const productsHtml = products.map(product => `
        <div class="col-md-4 mb-4">
            <div class="card h-100">
                ${product.image_path ? `
                    <img src="${product.image_path}" class="card-img-top" alt="${product.name}" style="height: 200px; object-fit: cover;">
                ` : `
                    <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 200px;">
                        <i class="fas fa-image fa-3x text-muted"></i>
                    </div>
                `}
                <div class="card-body d-flex flex-column">
                    <h5 class="card-title">${product.name}</h5>
                    <p class="card-text">${product.description || 'No description available'}</p>
                    <div class="mt-auto">
                        <div class="d-flex justify-content-between align-items-center">
                            <span class="badge bg-secondary">${product.category || 'Uncategorized'}</span>
                            <h5 class="text-primary mb-0">$${product.price || '0'}</h5>
                        </div>
                        ${product.ai_quality_score ? `
                            <div class="mt-2">
                                <small class="text-muted">AI Quality Score: ${Math.round(product.ai_quality_score * 100)}%</small>
                            </div>
                        ` : ''}
                        <button class="btn btn-primary btn-sm mt-2 w-100">View Details</button>
                    </div>
                </div>
            </div>
        </div>
    `).join('');
    
    grid.innerHTML = productsHtml;
}

function filterProducts() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    const category = document.getElementById('categoryFilter').value;
    
    let filteredProducts = allProducts.filter(product => {
        const matchesSearch = !searchTerm || 
            product.name.toLowerCase().includes(searchTerm) ||
            (product.description && product.description.toLowerCase().includes(searchTerm));
        
        const matchesCategory = !category || product.category === category;
        
        return matchesSearch && matchesCategory;
    });
    
    displayProducts(filteredProducts);
}
</script>
{% endblock %}
