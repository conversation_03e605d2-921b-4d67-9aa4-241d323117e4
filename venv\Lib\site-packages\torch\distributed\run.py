#!/usr/bin/env python3
"""
CraftConnect Application Runner
"""
import os
import sys
from app import app

def check_dependencies():
    """Check if all required packages are installed"""
    required_packages = [
        'flask', 'pymongo', 'werkzeug', 'matplotlib'
    ]
    
    missing = []
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing.append(package)
    
    if missing:
        print(f"❌ Missing packages: {', '.join(missing)}")
        print("Run: pip install -r requirements.txt")
        return False
    
    print("✅ All dependencies satisfied")
    return True

def check_mongodb():
    """Check MongoDB connection"""
    try:
        from pymongo import MongoClient
        client = MongoClient('mongodb://localhost:27017/', serverSelectionTimeoutMS=2000)
        client.server_info()
        print("✅ MongoDB connection successful")
        return True
    except Exception as e:
        print(f"❌ MongoDB connection failed: {e}")
        print("Make sure MongoDB is running on localhost:27017")
        return False

if __name__ == '__main__':
    print("🚀 Starting CraftConnect...")
    
    if not check_dependencies():
        sys.exit(1)
    
    if not check_mongodb():
        print("⚠️  Continuing without MongoDB (some features may not work)")
    
    print("🌐 Starting Flask server...")
    app.run(debug=True, host='0.0.0.0', port=5000)
