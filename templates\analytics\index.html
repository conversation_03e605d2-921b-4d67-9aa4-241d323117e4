{% extends "base.html" %}

{% block title %}Analytics - CraftConnect{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-md-12">
            <h1 class="mb-4">Analytics Dashboard</h1>
            
            <!-- Summary Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 id="totalViews">0</h4>
                                    <p class="mb-0">Total Views</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-eye fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 id="totalSales">$0</h4>
                                    <p class="mb-0">Total Sales</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-dollar-sign fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 id="avgRating">0.0</h4>
                                    <p class="mb-0">Avg Rating</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-star fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 id="conversionRate">0%</h4>
                                    <p class="mb-0">Conversion Rate</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-chart-line fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Charts Row -->
            <div class="row mb-4">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Sales Trend</h5>
                        </div>
                        <div class="card-body">
                            <canvas id="salesChart" width="400" height="200"></canvas>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Category Distribution</h5>
                        </div>
                        <div class="card-body">
                            <canvas id="categoryChart" width="400" height="200"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Product Performance -->
            <div class="row">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Top Performing Products</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Product</th>
                                            <th>Category</th>
                                            <th>Views</th>
                                            <th>Sales</th>
                                            <th>Revenue</th>
                                            <th>Rating</th>
                                        </tr>
                                    </thead>
                                    <tbody id="productTable">
                                        <tr>
                                            <td colspan="6" class="text-center text-muted">Loading product data...</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Sample data - in a real app, this would come from your API
const sampleData = {
    totalViews: 1250,
    totalSales: 3450,
    avgRating: 4.2,
    conversionRate: 12,
    salesData: {
        labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
        data: [500, 750, 1200, 800, 1500, 2100]
    },
    categoryData: {
        labels: ['Pottery', 'Textiles', 'Woodwork', 'Jewelry', 'Paintings'],
        data: [30, 25, 20, 15, 10]
    },
    topProducts: [
        { name: 'Handcrafted Ceramic Vase', category: 'Pottery', views: 245, sales: 12, revenue: 480, rating: 4.8 },
        { name: 'Woven Silk Scarf', category: 'Textiles', views: 189, sales: 8, revenue: 320, rating: 4.5 },
        { name: 'Wooden Jewelry Box', category: 'Woodwork', views: 156, sales: 6, revenue: 240, rating: 4.3 },
        { name: 'Silver Ring Set', category: 'Jewelry', views: 134, sales: 5, revenue: 200, rating: 4.6 },
        { name: 'Abstract Oil Painting', category: 'Paintings', views: 98, sales: 3, revenue: 150, rating: 4.1 }
    ]
};

document.addEventListener('DOMContentLoaded', function() {
    loadAnalyticsData();
    createCharts();
});

function loadAnalyticsData() {
    // Update summary cards
    document.getElementById('totalViews').textContent = sampleData.totalViews.toLocaleString();
    document.getElementById('totalSales').textContent = '$' + sampleData.totalSales.toLocaleString();
    document.getElementById('avgRating').textContent = sampleData.avgRating.toFixed(1);
    document.getElementById('conversionRate').textContent = sampleData.conversionRate + '%';
    
    // Update product table
    const tableBody = document.getElementById('productTable');
    const productsHtml = sampleData.topProducts.map(product => `
        <tr>
            <td>${product.name}</td>
            <td><span class="badge bg-secondary">${product.category}</span></td>
            <td>${product.views}</td>
            <td>${product.sales}</td>
            <td>$${product.revenue}</td>
            <td>
                <span class="text-warning">
                    ${'★'.repeat(Math.floor(product.rating))}${'☆'.repeat(5 - Math.floor(product.rating))}
                </span>
                ${product.rating}
            </td>
        </tr>
    `).join('');
    tableBody.innerHTML = productsHtml;
}

function createCharts() {
    // Sales Chart
    const salesCtx = document.getElementById('salesChart').getContext('2d');
    new Chart(salesCtx, {
        type: 'line',
        data: {
            labels: sampleData.salesData.labels,
            datasets: [{
                label: 'Sales ($)',
                data: sampleData.salesData.data,
                borderColor: 'rgb(75, 192, 192)',
                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
    
    // Category Chart
    const categoryCtx = document.getElementById('categoryChart').getContext('2d');
    new Chart(categoryCtx, {
        type: 'doughnut',
        data: {
            labels: sampleData.categoryData.labels,
            datasets: [{
                data: sampleData.categoryData.data,
                backgroundColor: [
                    '#FF6384',
                    '#36A2EB',
                    '#FFCE56',
                    '#4BC0C0',
                    '#9966FF'
                ]
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
}
</script>
{% endblock %}
