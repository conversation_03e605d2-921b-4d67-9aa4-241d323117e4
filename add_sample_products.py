#!/usr/bin/env python3
"""
<PERSON><PERSON>t to add sample products to the CraftConnect database
"""

from database import get_db
from datetime import datetime, timezone
import random

def create_sample_users():
    """Create sample artisan users"""
    db = get_db()
    
    sample_users = [
        {
            'name': '<PERSON>',
            'email': '<EMAIL>',
            'password': 'hashed_password_123',  # In real app, this would be properly hashed
            'role': 'artisan',
            'location': 'Santa Fe, New Mexico',
            'specialty': 'Traditional Pottery',
            'created_at': datetime.now(timezone.utc)
        },
        {
            'name': '<PERSON>',
            'email': '<EMAIL>',
            'password': 'hashed_password_456',
            'role': 'artisan',
            'location': 'Portland, Oregon',
            'specialty': 'Fine Woodworking',
            'created_at': datetime.now(timezone.utc)
        },
        {
            'name': '<PERSON><PERSON>',
            'email': '<EMAIL>',
            'password': 'hashed_password_789',
            'role': 'artisan',
            'location': 'Brooklyn, New York',
            'specialty': 'Textile Arts',
            'created_at': datetime.now(timezone.utc)
        },
        {
            'name': '<PERSON>stein',
            'email': '<EMAIL>',
            'password': 'hashed_password_101',
            'role': 'artisan',
            'location': 'Asheville, North Carolina',
            'specialty': 'Metalwork & Jewelry',
            'created_at': datetime.now(timezone.utc)
        },
        {
            'name': 'Isabella Rossi',
            'email': '<EMAIL>',
            'password': 'hashed_password_202',
            'role': 'artisan',
            'location': 'Tuscany, Italy',
            'specialty': 'Traditional Paintings',
            'created_at': datetime.now(timezone.utc)
        }
    ]
    
    user_ids = []
    for user in sample_users:
        # Check if user already exists
        existing = db.users.find_one({'email': user['email']})
        if not existing:
            result = db.users.insert_one(user)
            user_ids.append(str(result.inserted_id))
            print(f"✓ Created user: {user['name']}")
        else:
            user_ids.append(str(existing['_id']))
            print(f"→ User already exists: {user['name']}")
    
    return user_ids

def create_sample_products(user_ids):
    """Create sample products for the artisans"""
    db = get_db()
    
    sample_products = [
        # Pottery Products
        {
            'user_id': user_ids[0],
            'name': 'Handcrafted Ceramic Vase',
            'description': 'Beautiful ceramic vase with traditional Southwest patterns. Hand-thrown on the wheel and fired in a wood kiln. Perfect for displaying fresh flowers or as a standalone decorative piece.',
            'category': 'pottery',
            'price': 85.00,
            'quantity': 3,
            'ai_quality_score': 0.92,
            'authenticity_hash': 'pot_001_authentic',
            'materials': ['Clay', 'Natural Glazes', 'Wood Ash'],
            'dimensions': '12" H x 6" W',
            'weight': '2.5 lbs',
            'created_at': datetime.now(timezone.utc)
        },
        {
            'user_id': user_ids[0],
            'name': 'Rustic Dinner Plate Set',
            'description': 'Set of 4 handmade dinner plates with earthy glazes. Each plate is unique with subtle variations that showcase the artisan touch. Microwave and dishwasher safe.',
            'category': 'pottery',
            'price': 120.00,
            'quantity': 2,
            'ai_quality_score': 0.89,
            'authenticity_hash': 'pot_002_authentic',
            'materials': ['Stoneware Clay', 'Food-Safe Glazes'],
            'dimensions': '10.5" diameter',
            'weight': '3.2 lbs (set)',
            'created_at': datetime.now(timezone.utc)
        },
        {
            'user_id': user_ids[0],
            'name': 'Artisan Coffee Mug',
            'description': 'Hand-thrown coffee mug with a comfortable handle and beautiful blue-green glaze. Holds 12oz and keeps your coffee warm longer than mass-produced mugs.',
            'category': 'pottery',
            'price': 28.00,
            'quantity': 8,
            'ai_quality_score': 0.87,
            'authenticity_hash': 'pot_003_authentic',
            'materials': ['Porcelain', 'Celadon Glaze'],
            'dimensions': '4" H x 3.5" W',
            'weight': '0.8 lbs',
            'created_at': datetime.now(timezone.utc)
        },
        
        # Woodwork Products
        {
            'user_id': user_ids[1],
            'name': 'Handcrafted Walnut Jewelry Box',
            'description': 'Elegant jewelry box made from sustainably sourced black walnut. Features multiple compartments, soft felt lining, and traditional dovetail joints. A perfect heirloom piece.',
            'category': 'woodwork',
            'price': 185.00,
            'quantity': 1,
            'ai_quality_score': 0.95,
            'authenticity_hash': 'wood_001_authentic',
            'materials': ['Black Walnut', 'Felt Lining', 'Brass Hardware'],
            'dimensions': '10" L x 7" W x 4" H',
            'weight': '2.8 lbs',
            'created_at': datetime.now(timezone.utc)
        },
        {
            'user_id': user_ids[1],
            'name': 'Live Edge Cutting Board',
            'description': 'Beautiful cutting board made from a single piece of maple with natural live edge. Food-safe finish and perfect for both cooking and serving cheese and charcuterie.',
            'category': 'woodwork',
            'price': 75.00,
            'quantity': 4,
            'ai_quality_score': 0.91,
            'authenticity_hash': 'wood_002_authentic',
            'materials': ['Maple Wood', 'Food-Safe Mineral Oil'],
            'dimensions': '16" L x 10" W x 1.5" H',
            'weight': '3.5 lbs',
            'created_at': datetime.now(timezone.utc)
        },
        {
            'user_id': user_ids[1],
            'name': 'Handturned Wooden Bowl',
            'description': 'Stunning bowl turned from a single piece of cherry wood. The natural grain patterns make each bowl unique. Perfect for salads, fruit, or decorative use.',
            'category': 'woodwork',
            'price': 65.00,
            'quantity': 6,
            'ai_quality_score': 0.88,
            'authenticity_hash': 'wood_003_authentic',
            'materials': ['Cherry Wood', 'Natural Wax Finish'],
            'dimensions': '12" diameter x 4" H',
            'weight': '1.8 lbs',
            'created_at': datetime.now(timezone.utc)
        },
        
        # Textile Products
        {
            'user_id': user_ids[2],
            'name': 'Hand-Woven Silk Scarf',
            'description': 'Luxurious silk scarf hand-woven on a traditional loom. Features intricate geometric patterns inspired by West African textiles. Naturally dyed with plant-based colors.',
            'category': 'textiles',
            'price': 95.00,
            'quantity': 5,
            'ai_quality_score': 0.93,
            'authenticity_hash': 'text_001_authentic',
            'materials': ['100% Silk', 'Natural Plant Dyes'],
            'dimensions': '60" L x 12" W',
            'weight': '0.3 lbs',
            'created_at': datetime.now(timezone.utc)
        },
        {
            'user_id': user_ids[2],
            'name': 'Organic Cotton Throw Blanket',
            'description': 'Cozy throw blanket made from organic cotton and hand-dyed with indigo. Perfect for snuggling on the couch or adding a pop of color to your bedroom.',
            'category': 'textiles',
            'price': 145.00,
            'quantity': 3,
            'ai_quality_score': 0.90,
            'authenticity_hash': 'text_002_authentic',
            'materials': ['Organic Cotton', 'Natural Indigo Dye'],
            'dimensions': '50" x 60"',
            'weight': '2.2 lbs',
            'created_at': datetime.now(timezone.utc)
        },
        
        # Jewelry & Metalwork
        {
            'user_id': user_ids[3],
            'name': 'Sterling Silver Ring Set',
            'description': 'Set of three stackable sterling silver rings with hammered texture. Can be worn together or separately. Each ring is hand-forged and unique.',
            'category': 'jewelry',
            'price': 125.00,
            'quantity': 4,
            'ai_quality_score': 0.94,
            'authenticity_hash': 'jewel_001_authentic',
            'materials': ['Sterling Silver'],
            'dimensions': 'Sizes 6, 7, 8 available',
            'weight': '0.2 lbs',
            'created_at': datetime.now(timezone.utc)
        },
        {
            'user_id': user_ids[3],
            'name': 'Copper Wind Chimes',
            'description': 'Handcrafted wind chimes made from recycled copper pipes. Creates beautiful, soothing tones in the breeze. Weather-resistant finish for outdoor use.',
            'category': 'metalwork',
            'price': 78.00,
            'quantity': 7,
            'ai_quality_score': 0.86,
            'authenticity_hash': 'metal_001_authentic',
            'materials': ['Recycled Copper', 'Hemp Cord'],
            'dimensions': '24" L total',
            'weight': '1.5 lbs',
            'created_at': datetime.now(timezone.utc)
        },
        
        # Paintings
        {
            'user_id': user_ids[4],
            'name': 'Tuscan Landscape Oil Painting',
            'description': 'Original oil painting of the rolling hills of Tuscany during golden hour. Painted en plein air using traditional techniques passed down through generations.',
            'category': 'paintings',
            'price': 450.00,
            'quantity': 1,
            'ai_quality_score': 0.97,
            'authenticity_hash': 'paint_001_authentic',
            'materials': ['Oil Paint', 'Canvas', 'Wooden Frame'],
            'dimensions': '16" x 20" (framed)',
            'weight': '3.0 lbs',
            'created_at': datetime.now(timezone.utc)
        },
        {
            'user_id': user_ids[4],
            'name': 'Abstract Watercolor Series',
            'description': 'Set of three abstract watercolor paintings inspired by natural elements. Each piece flows into the next, creating a cohesive collection perfect for modern spaces.',
            'category': 'paintings',
            'price': 280.00,
            'quantity': 1,
            'ai_quality_score': 0.91,
            'authenticity_hash': 'paint_002_authentic',
            'materials': ['Watercolor Paint', 'Watercolor Paper'],
            'dimensions': '11" x 14" each (set of 3)',
            'weight': '1.2 lbs',
            'created_at': datetime.now(timezone.utc)
        }
    ]
    
    product_ids = []
    for product in sample_products:
        result = db.products.insert_one(product)
        product_ids.append(str(result.inserted_id))
        print(f"✓ Created product: {product['name']} - ${product['price']}")
    
    return product_ids

def add_sample_reviews(product_ids, user_ids):
    """Add sample reviews for products"""
    db = get_db()
    
    sample_reviews = [
        {
            'product_id': product_ids[0],
            'user_id': user_ids[1],  # Different user reviewing
            'rating': 5,
            'comment': 'Absolutely beautiful vase! The craftsmanship is exceptional and it looks perfect in my living room.',
            'created_at': datetime.now(timezone.utc)
        },
        {
            'product_id': product_ids[3],
            'user_id': user_ids[2],
            'rating': 5,
            'comment': 'This jewelry box is a work of art. The dovetail joints are perfect and the walnut is gorgeous.',
            'created_at': datetime.now(timezone.utc)
        },
        {
            'product_id': product_ids[6],
            'user_id': user_ids[0],
            'rating': 4,
            'comment': 'Beautiful scarf with amazing colors. The silk is high quality and the weaving is intricate.',
            'created_at': datetime.now(timezone.utc)
        }
    ]
    
    for review in sample_reviews:
        db.reviews.insert_one(review)
        print(f"✓ Added review for product")

def main():
    """Main function to populate the database"""
    print("🎨 Adding Sample Products to CraftConnect Database")
    print("=" * 55)
    
    try:
        # Create sample users
        print("\n👥 Creating Sample Artisan Users...")
        user_ids = create_sample_users()
        
        # Create sample products
        print(f"\n🛍️ Creating Sample Products...")
        product_ids = create_sample_products(user_ids)
        
        # Add sample reviews
        print(f"\n⭐ Adding Sample Reviews...")
        add_sample_reviews(product_ids, user_ids)
        
        print(f"\n✅ Successfully added {len(product_ids)} products to the database!")
        print("🌐 You can now view them in the marketplace at: http://127.0.0.1:5000/marketplace/")
        
    except Exception as e:
        print(f"❌ Error adding sample data: {str(e)}")

if __name__ == "__main__":
    main()
